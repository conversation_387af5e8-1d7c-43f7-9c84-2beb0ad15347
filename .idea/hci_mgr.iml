<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Python" name="Python facet">
      <configuration sdkName="Python 3.10 (hci_mgr)" />
    </facet>
  </component>
  <component name="Go" enabled="true" />
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$/../hci_api" />
    <content url="file://$MODULE_DIR$/../hci_asyn" />
    <content url="file://$MODULE_DIR$/../hci_db" />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/.venv" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Python 3.10 (hci_mgr) interpreter library" level="application" />
  </component>
</module>