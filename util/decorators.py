# decorators.py

from functools import wraps
import json
import traceback
from tornado.web import HTTPError
from api.log.log import CustomLogger

import logging
logger = logging.getLogger(__name__)
new_logger = CustomLogger()


def error_decorator(error_message,ob=None, action=None,desc=None):
    def decorator(func):
        @wraps(func)
        def wrapper(handler, *args, **kwargs):
            username = handler.get_cookie("username", "")
            role = handler.get_cookie("role", "")
           
            try:
                # 获取json数据， strip()：去除请求体两边的空白字符串
                request_body = handler.request.body.decode("utf-8").strip()
                if request_body:
                    try:
                        request_data = json.loads(request_body)
                        name = request_data.get("name", "") # 操作对象的名字
                    except json.JSONDecodeError:
                        print("error_decorator函数 json解析错误")
                        name = ""
                # 调用原始decorated函数，并传入处理后的参数和自定义数据
                result = func(handler, *args, **kwargs)
                if ob != None and action!= None and desc!= None:
                    new_logger.log(
                        username, ob, action, "成功", role,"{}: {},成功".format(desc, name)
                    )   
                return result
            except Exception as e:
                traceback.print_exc()
                if ob != None and action!= None and desc!= None:
                    new_logger.log(
                        username, ob, action, "失败", role,"{}: {},失败".format(desc, name)
                    )   
                handler.set_status(502)
                handler.write({"code": 502, "msg": error_message, "error": str(e)})
                return handler.finish()
        return wrapper

    return decorator

